<?php

declare(strict_types=1);

use Phinx\Migration\AbstractMigration;

final class CreateUserAnalyticsTables extends AbstractMigration
{
    /**
     * Change Method.
     *
     * Write your reversible migrations using this method.
     *
     * More information on writing migrations is available here:
     * https://book.cakephp.org/phinx/0/en/migrations.html#the-change-method
     *
     * Remember to call "create()" or "update()" and NOT "save()" when working
     * with the Table class.
     */
    public function change(): void
    {
        // Create daily user analytics summary table (channel-level aggregation)
        $this->createDailyChannelUserAnalyticsTable();

        // Create daily game user analytics summary table (game-level aggregation)
        $this->createDailyGameUserAnalyticsTable();

        // Create user first activity tracking table (for new user identification)
        $this->createUserFirstActivityTable();

        // Create user retention tracking table (for retention rate calculations)
        $this->createUserRetentionTable();
    }

    /**
     * Create daily channel-level user analytics summary table.
     */
    private function createDailyChannelUserAnalyticsTable(): void
    {
        $table = $this->table('fact_daily_channel_user_analytics', [
            'id' => false,
            'comment' => '每日渠道用户分析统计表',
        ]);

        $table->addColumn('date', 'date', ['null' => false, 'comment' => '统计日期'])
            ->addColumn('channel_id', 'integer', ['signed' => false, 'null' => false, 'comment' => '渠道'])
            ->addColumn('daily_active_users', 'integer', ['signed' => false, 'null' => false, 'default' => 0, 'comment' => '活跃用户数'])
            ->addColumn('total_sessions', 'integer', ['signed' => false, 'null' => false, 'default' => 0, 'comment' => '活跃用户总访问次数'])
            ->addColumn('total_session_duration_seconds', 'biginteger', ['signed' => false, 'null' => false, 'default' => 0, 'comment' => '活跃用户总停留时长(秒)'])
            ->addColumn('avg_session_duration_seconds', 'decimal', ['precision' => 10, 'scale' => 2, 'null' => false, 'default' => 0.00, 'comment' => '活跃用户平均停留时长'])
            ->addColumn('avg_sessions_per_user', 'decimal', ['precision' => 10, 'scale' => 2, 'null' => false, 'default' => 0.00, 'comment' => '活跃用户人均访问次数'])
            ->addColumn('total_heartbeats', 'biginteger', ['signed' => false, 'null' => false, 'default' => 0, 'comment' => '活跃用户总心跳次数'])
            ->addColumn('avg_heartbeats_per_session', 'decimal', ['precision' => 10, 'scale' => 2, 'null' => false, 'default' => 0.00, 'comment' => '活跃用户平均每会话心跳次数'])
            // 活跃用户相关指标（排除新增用户）
            ->addColumn('active_users_count', 'integer', ['signed' => false, 'null' => false, 'default' => 0, 'comment' => '老用户数'])
            ->addColumn('active_users_sessions', 'integer', ['signed' => false, 'null' => false, 'default' => 0, 'comment' => '老用户总访问次数'])
            ->addColumn('active_users_duration_seconds', 'biginteger', ['signed' => false, 'null' => false, 'default' => 0, 'comment' => '老用户总访问时长（秒）'])
            ->addColumn('active_users_avg_session_duration', 'decimal', ['precision' => 10, 'scale' => 2, 'null' => false, 'default' => 0.00, 'comment' => '老用户平均停留时长'])
            ->addColumn('active_users_avg_sessions_per_user', 'decimal', ['precision' => 10, 'scale' => 2, 'null' => false, 'default' => 0.00, 'comment' => '老用户平均访问次数'])
            // 新增用户相关指标
            ->addColumn('daily_new_users', 'integer', ['signed' => false, 'null' => false, 'default' => 0, 'comment' => '新增用户数'])
            ->addColumn('new_users_sessions', 'integer', ['signed' => false, 'null' => false, 'default' => 0, 'comment' => '新增用户总访问次数'])
            ->addColumn('new_users_duration_seconds', 'biginteger', ['signed' => false, 'null' => false, 'default' => 0, 'comment' => '新增用户总访问时长（秒）'])
            ->addColumn('new_users_avg_session_duration', 'decimal', ['precision' => 10, 'scale' => 2, 'null' => false, 'default' => 0.00, 'comment' => '新增用户平均停留时长'])
            ->addColumn('new_users_avg_sessions_per_user', 'decimal', ['precision' => 10, 'scale' => 2, 'null' => false, 'default' => 0.00, 'comment' => '新增用户平均访问次数'])
            
            ->addColumn('day_1_retention_rate', 'decimal', ['precision' => 5, 'scale' => 4, 'null' => true, 'comment' => '活跃用户次日留存率(0-1)'])
            ->addColumn('day_3_retention_rate', 'decimal', ['precision' => 5, 'scale' => 4, 'null' => true, 'comment' => '活跃用户3日留存率(0-1)'])
            ->addColumn('day_7_retention_rate', 'decimal', ['precision' => 5, 'scale' => 4, 'null' => true, 'comment' => '活跃用户7日留存率(0-1)'])
            ->addColumn('day_30_retention_rate', 'decimal', ['precision' => 5, 'scale' => 4, 'null' => true, 'comment' => '活跃用户30日留存率(0-1)'])
            // 新增用户留存率
            ->addColumn('new_users_day_1_retention_rate', 'decimal', ['precision' => 5, 'scale' => 4, 'null' => true, 'comment' => '新增用户次日留存率(0-1)'])
            ->addColumn('new_users_day_3_retention_rate', 'decimal', ['precision' => 5, 'scale' => 4, 'null' => true, 'comment' => '新增用户3日留存率(0-1)'])
            ->addColumn('new_users_day_7_retention_rate', 'decimal', ['precision' => 5, 'scale' => 4, 'null' => true, 'comment' => '新增用户7日留存率(0-1)'])
            ->addColumn('new_users_day_30_retention_rate', 'decimal', ['precision' => 5, 'scale' => 4, 'null' => true, 'comment' => '新增用户30日留存率(0-1)'])
            // 活跃用户留存率
            ->addColumn('active_users_day_1_retention_rate', 'decimal', ['precision' => 5, 'scale' => 4, 'null' => true, 'comment' => '老用户次日留存率(0-1)'])
            ->addColumn('active_users_day_3_retention_rate', 'decimal', ['precision' => 5, 'scale' => 4, 'null' => true, 'comment' => '老用户3日留存率(0-1)'])
            ->addColumn('active_users_day_7_retention_rate', 'decimal', ['precision' => 5, 'scale' => 4, 'null' => true, 'comment' => '老用户7日留存率(0-1)'])
            ->addColumn('active_users_day_30_retention_rate', 'decimal', ['precision' => 5, 'scale' => 4, 'null' => true, 'comment' => '老用户30日留存率(0-1)'])
            ->addColumn('created_at', 'datetime', ['null' => true, 'comment' => '创建时间'])
            ->addColumn('updated_at', 'datetime', ['null' => true, 'comment' => '更新时间'])

            // Primary key and unique constraints
            ->addIndex(['date', 'channel_id'], ['unique' => true, 'name' => 'uk_date_channel'])

            // Performance indexes
            ->addIndex(['date'], ['name' => 'idx_date'])
            ->addIndex(['channel_id'], ['name' => 'idx_channel_id'])
            ->addIndex(['daily_active_users'], ['name' => 'idx_dau'])
            ->addIndex(['daily_new_users'], ['name' => 'idx_new_users'])
            ->create();
    }

    /**
     * Create daily game-level user analytics summary table.
     */
    private function createDailyGameUserAnalyticsTable(): void
    {
        $table = $this->table('fact_daily_game_user_analytics', [
            'id' => false,
            'comment' => '每日游戏用户分析统计表',
        ]);

        $table->addColumn('date', 'date', ['null' => false, 'comment' => '统计日期'])
            ->addColumn('channel_id', 'integer', ['signed' => false, 'null' => false, 'comment' => '渠道'])
            ->addColumn('game_id', 'integer', ['signed' => false, 'null' => false, 'comment' => '游戏'])
            ->addColumn('daily_active_users', 'integer', ['signed' => false, 'null' => false, 'default' => 0, 'comment' => '活跃用户数'])
            ->addColumn('total_sessions', 'integer', ['signed' => false, 'null' => false, 'default' => 0, 'comment' => '活跃用户总访问次数'])
            ->addColumn('total_session_duration_seconds', 'biginteger', ['signed' => false, 'null' => false, 'default' => 0, 'comment' => '活跃用户总停留时长(秒)'])
            ->addColumn('avg_session_duration_seconds', 'decimal', ['precision' => 10, 'scale' => 2, 'null' => false, 'default' => 0.00, 'comment' => '活跃用户平均停留时长(秒)'])
            ->addColumn('avg_sessions_per_user', 'decimal', ['precision' => 10, 'scale' => 2, 'null' => false, 'default' => 0.00, 'comment' => '活跃用户人均访问次数'])
            ->addColumn('total_heartbeats', 'biginteger', ['signed' => false, 'null' => false, 'default' => 0, 'comment' => '活跃用户总心跳次数'])
            ->addColumn('avg_heartbeats_per_session', 'decimal', ['precision' => 10, 'scale' => 2, 'null' => false, 'default' => 0.00, 'comment' => '活跃用户平均每会话心跳次数'])
            // 活跃用户相关指标（排除新增用户）
            ->addColumn('active_users_count', 'integer', ['signed' => false, 'null' => false, 'default' => 0, 'comment' => '老用户数'])
            ->addColumn('active_users_sessions', 'integer', ['signed' => false, 'null' => false, 'default' => 0, 'comment' => '老用户总访问次数'])
            ->addColumn('active_users_duration_seconds', 'biginteger', ['signed' => false, 'null' => false, 'default' => 0, 'comment' => '老用户总访问时长（秒）'])
            ->addColumn('active_users_avg_session_duration', 'decimal', ['precision' => 10, 'scale' => 2, 'null' => false, 'default' => 0.00, 'comment' => '老用户平均停留时长'])
            ->addColumn('active_users_avg_sessions_per_user', 'decimal', ['precision' => 10, 'scale' => 2, 'null' => false, 'default' => 0.00, 'comment' => '老用户平均访问次数'])
            // 新增用户相关指标
            ->addColumn('daily_new_users', 'integer', ['signed' => false, 'null' => false, 'default' => 0, 'comment' => '新增用户数'])
            ->addColumn('new_users_sessions', 'integer', ['signed' => false, 'null' => false, 'default' => 0, 'comment' => '新增用户总访问次数'])
            ->addColumn('new_users_duration_seconds', 'biginteger', ['signed' => false, 'null' => false, 'default' => 0, 'comment' => '新增用户总访问时长（秒）'])
            ->addColumn('new_users_avg_session_duration', 'decimal', ['precision' => 10, 'scale' => 2, 'null' => false, 'default' => 0.00, 'comment' => '新增用户平均停留时长'])
            ->addColumn('new_users_avg_sessions_per_user', 'decimal', ['precision' => 10, 'scale' => 2, 'null' => false, 'default' => 0.00, 'comment' => '新增用户平均访问次数'])
            
            // 总体留存率
            ->addColumn('day_1_retention_rate', 'decimal', ['precision' => 5, 'scale' => 4, 'null' => true, 'comment' => '活跃用户次日留存率(0-1)'])
            ->addColumn('day_3_retention_rate', 'decimal', ['precision' => 5, 'scale' => 4, 'null' => true, 'comment' => '活跃用户3日留存率(0-1)'])
            ->addColumn('day_7_retention_rate', 'decimal', ['precision' => 5, 'scale' => 4, 'null' => true, 'comment' => '活跃用户7日留存率(0-1)'])
            ->addColumn('day_30_retention_rate', 'decimal', ['precision' => 5, 'scale' => 4, 'null' => true, 'comment' => '活跃用户30日留存率(0-1)'])
            // 新增用户留存率
            ->addColumn('new_users_day_1_retention_rate', 'decimal', ['precision' => 5, 'scale' => 4, 'null' => true, 'comment' => '新增用户次日留存率(0-1)'])
            ->addColumn('new_users_day_3_retention_rate', 'decimal', ['precision' => 5, 'scale' => 4, 'null' => true, 'comment' => '新增用户3日留存率(0-1)'])
            ->addColumn('new_users_day_7_retention_rate', 'decimal', ['precision' => 5, 'scale' => 4, 'null' => true, 'comment' => '新增用户7日留存率(0-1)'])
            ->addColumn('new_users_day_30_retention_rate', 'decimal', ['precision' => 5, 'scale' => 4, 'null' => true, 'comment' => '新增用户30日留存率(0-1)'])
            // 活跃用户留存率
            ->addColumn('active_users_day_1_retention_rate', 'decimal', ['precision' => 5, 'scale' => 4, 'null' => true, 'comment' => '老用户次日留存率(0-1)'])
            ->addColumn('active_users_day_3_retention_rate', 'decimal', ['precision' => 5, 'scale' => 4, 'null' => true, 'comment' => '老用户3日留存率(0-1)'])
            ->addColumn('active_users_day_7_retention_rate', 'decimal', ['precision' => 5, 'scale' => 4, 'null' => true, 'comment' => '老用户7日留存率(0-1)'])
            ->addColumn('active_users_day_30_retention_rate', 'decimal', ['precision' => 5, 'scale' => 4, 'null' => true, 'comment' => '老用户30日留存率(0-1)'])
            ->addColumn('created_at', 'datetime', ['null' => true, 'comment' => '创建时间'])
            ->addColumn('updated_at', 'datetime', ['null' => true, 'comment' => '更新时间'])

            // Primary key and unique constraints
            ->addIndex(['date', 'channel_id', 'game_id'], ['unique' => true, 'name' => 'uk_date_channel_game'])

            // Performance indexes
            ->addIndex(['date'], ['name' => 'idx_date'])
            ->addIndex(['channel_id'], ['name' => 'idx_channel_id'])
            ->addIndex(['game_id'], ['name' => 'idx_game_id'])
            ->addIndex(['channel_id', 'game_id'], ['name' => 'idx_channel_game'])
            ->addIndex(['daily_active_users'], ['name' => 'idx_dau'])
            ->addIndex(['daily_new_users'], ['name' => 'idx_new_users'])
            ->create();
    }

    /**
     * Create user first activity tracking table.
     */
    private function createUserFirstActivityTable(): void
    {
        $table = $this->table('user_first_activity', [
            'id' => false,
            'comment' => '用户首次活动记录表',
        ]);

        $table->addColumn('user_id', 'integer', ['signed' => false, 'null' => false, 'comment' => '用户'])
            ->addColumn('channel_id', 'integer', ['signed' => false, 'null' => false, 'comment' => '渠道'])
            ->addColumn('game_id', 'integer', ['signed' => false, 'null' => false, 'comment' => '游戏'])
            ->addColumn('first_activity_date', 'date', ['null' => false, 'comment' => '首次活动日期'])
            ->addColumn('first_session_id', 'string', ['limit' => 128, 'null' => false, 'comment' => '首次会话ID'])
            ->addColumn('first_session_start_time', 'biginteger', ['signed' => false, 'null' => false, 'comment' => '首次会话开始时间戳(毫秒)'])
            ->addColumn('registration_date', 'date', ['null' => true, 'comment' => '用户注册日期'])
            ->addColumn('is_new_user', 'boolean', ['null' => false, 'default' => true, 'comment' => '是否为新用户'])
            ->addColumn('created_at', 'datetime', ['null' => true, 'comment' => '创建时间'])
            ->addColumn('updated_at', 'datetime', ['null' => true, 'comment' => '更新时间'])

            // Primary key and unique constraints
            ->addIndex(['user_id', 'channel_id', 'game_id'], ['unique' => true, 'name' => 'uk_user_channel_game'])

            // Performance indexes
            ->addIndex(['first_activity_date'], ['name' => 'idx_first_activity_date'])
            ->addIndex(['channel_id', 'first_activity_date'], ['name' => 'idx_channel_first_activity'])
            ->addIndex(['game_id', 'first_activity_date'], ['name' => 'idx_game_first_activity'])
            ->addIndex(['channel_id', 'game_id', 'first_activity_date'], ['name' => 'idx_channel_game_first_activity'])
            ->addIndex(['is_new_user', 'first_activity_date'], ['name' => 'idx_new_user_activity'])
            ->addIndex(['registration_date'], ['name' => 'idx_registration_date'])
            ->create();
    }

    /**
     * Create user retention tracking table.
     */
    private function createUserRetentionTable(): void
    {
        $table = $this->table('user_retention_tracking', [
            'id' => false,
            'comment' => '用户留存跟踪表',
        ]);

        $table->addColumn('user_id', 'integer', ['signed' => false, 'null' => false, 'comment' => '用户'])
            ->addColumn('channel_id', 'integer', ['signed' => false, 'null' => false, 'comment' => '渠道'])
            ->addColumn('game_id', 'integer', ['signed' => false, 'null' => false, 'comment' => '游戏'])
            ->addColumn('cohort_date', 'date', ['null' => false, 'comment' => '用户群组日期(首次活动日期)'])
            ->addColumn('activity_date', 'date', ['null' => false, 'comment' => '活动日期'])
            ->addColumn('days_since_first_activity', 'integer', ['signed' => false, 'null' => false, 'comment' => '距离首次活动天数'])
            ->addColumn('session_count', 'integer', ['signed' => false, 'null' => false, 'default' => 0, 'comment' => '当日访问次数'])
            ->addColumn('total_session_duration_seconds', 'integer', ['signed' => false, 'null' => false, 'default' => 0, 'comment' => '当日总停留时长(秒)'])
            ->addColumn('heartbeat_count', 'integer', ['signed' => false, 'null' => false, 'default' => 0, 'comment' => '当日心跳次数'])
            ->addColumn('created_at', 'datetime', ['null' => true, 'comment' => '创建时间'])
            ->addColumn('updated_at', 'datetime', ['null' => true, 'comment' => '更新时间'])

            // Primary key and unique constraints
            ->addIndex(['user_id', 'channel_id', 'game_id', 'activity_date'], ['unique' => true, 'name' => 'uk_user_channel_game_date'])

            // Performance indexes for retention analysis
            ->addIndex(['cohort_date'], ['name' => 'idx_cohort_date'])
            ->addIndex(['activity_date'], ['name' => 'idx_activity_date'])
            ->addIndex(['channel_id', 'cohort_date'], ['name' => 'idx_channel_cohort'])
            ->addIndex(['game_id', 'cohort_date'], ['name' => 'idx_game_cohort'])
            ->addIndex(['channel_id', 'game_id', 'cohort_date'], ['name' => 'idx_channel_game_cohort'])
            ->addIndex(['days_since_first_activity'], ['name' => 'idx_days_since_first'])
            ->addIndex(['cohort_date', 'days_since_first_activity'], ['name' => 'idx_cohort_days_since'])
            ->create();
    }
}
