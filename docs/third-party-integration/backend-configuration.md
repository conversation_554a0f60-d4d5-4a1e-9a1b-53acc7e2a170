# Backend Configuration Guide

## Overview

This guide explains how to configure our backend systems to support new third-party integrations. It covers channel setup, parameter strategies, game configuration, and security settings.

## Channel Configuration

### 1. Create Channel Record

Add a new channel in the admin panel or database:

```sql
INSERT INTO channels (code, name, status, created_at, updated_at) 
VALUES ('partner123', 'Partner Company Name', 1, NOW(), NOW());
```

**Channel Fields:**
- `code`: Unique identifier for the partner (alphanumeric, underscores allowed)
- `name`: Human-readable partner name
- `status`: 1 = active, 0 = inactive

### 2. Configure Channel Parameter Strategy

Edit `/config/channel_strategies.php` to define how user parameters are extracted:

```php
<?php
return [
    'strategies' => [
        // Example: Partner uses 'user_id' parameter
        123 => CustomChannelParameterStrategy::forParameter('user_id'),
        
        // Example: Partner uses multiple possible parameters
        124 => CustomChannelParameterStrategy::forParameters(['uid', 'user_id', 'player_id']),
        
        // Example: Partner uses custom parameter name
        125 => CustomChannelParameterStrategy::forParameter('custom_player_id'),
    ],
    
    // Default parameters if no strategy is defined
    'default_parameters' => [
        'id',
        'user_id', 
        'player_id',
        'uid',
        'username',
    ],
    
    // Validation rules for user identifiers
    'validation' => [
        'max_length' => 50,
        'pattern' => '/^[a-zA-Z0-9_\-@.]+$/',
        'min_length' => 1,
    ],
    
    // Auto-registration settings
    'auto_register' => [
        'enabled' => true,
        'default_avatar' => '/app/user/default-avatar.png',
        'password_prefix' => 'auto_',
    ],
];
```

### 3. Parameter Strategy Examples

#### Single Parameter Strategy
```php
// Partner always uses 'player_id' parameter
125 => CustomChannelParameterStrategy::forParameter('player_id'),
```

#### Multiple Parameter Strategy (Priority Order)
```php
// Try 'uid' first, then 'user_id', then 'id'
126 => CustomChannelParameterStrategy::forParameters(['uid', 'user_id', 'id']),
```

#### Custom Parameter Names
```php
// Partner uses non-standard parameter names
127 => CustomChannelParameterStrategy::forParameter('openid'), // WeChat
128 => CustomChannelParameterStrategy::forParameter('account'), // Custom system
```

## Game Configuration

### 1. Create Game Record

Add game configuration in the admin panel:

```sql
INSERT INTO games (code, name, cover, channel_ids, status, latest_version, created_at, updated_at)
VALUES (1001, 'Puzzle Game', '/images/puzzle-cover.jpg', '123,124,125', 1, '1.0.0', NOW(), NOW());
```

**Game Fields:**
- `code`: Unique numeric identifier for the game
- `name`: Game display name
- `cover`: Cover image URL
- `channel_ids`: Comma-separated list of allowed channel IDs
- `status`: 1 = active, 0 = inactive
- `latest_version`: Current game version

### 2. Game-Channel Association

The `channel_ids` field controls which channels can access which games:

```php
// Example: Game 1001 is available to channels 123, 124, and 125
'channel_ids' => '123,124,125'

// Example: Game 1002 is only available to channel 123
'channel_ids' => '123'
```

### 3. Game File Management

Games are stored in the following directory structure:
```
/public/game/{GAME_CODE}/{VERSION}/
├── index.html          # Main game file
├── assets/            # Game assets
├── js/               # JavaScript files
└── css/              # Stylesheets
```

## Security Configuration

### 1. CORS Settings

Configure CORS for direct SDK integration in `/config/cors.php`:

```php
<?php
return [
    'allowed_origins' => [
        'https://partner-domain.com',
        'https://cdn.partner-domain.com',
        'https://games.partner-domain.com',
    ],
    'allowed_methods' => ['GET', 'POST', 'OPTIONS'],
    'allowed_headers' => ['Content-Type', 'X-Channel-Code', 'Authorization'],
    'exposed_headers' => [],
    'max_age' => 86400,
    'supports_credentials' => true,
];
```

### 2. Rate Limiting

API endpoints have built-in rate limiting. Configure in controller annotations:

```php
#[RateLimiter(ttl: 60, limit: 10)] // 10 requests per minute
public function heartbeat(Request $request): Response
```

### 3. Domain Validation

For iframe integration, validate allowed domains in the game controller:

```php
// Add domain validation logic
$allowedDomains = [
    'partner-domain.com',
    'cdn.partner-domain.com',
];

$referer = $request->header('Referer');
if (!$this->isAllowedDomain($referer, $allowedDomains)) {
    return response('Forbidden', 403);
}
```

## Database Schema

### Required Tables

1. **channels** - Channel configuration
2. **games** - Game configuration  
3. **wa_users** - User accounts
4. **heartbeat_sessions** - Session tracking
5. **player_data_backups** - Game data storage

### Channel Table Structure
```sql
CREATE TABLE channels (
    id INT PRIMARY KEY AUTO_INCREMENT,
    code VARCHAR(50) UNIQUE NOT NULL,
    name VARCHAR(255) NOT NULL,
    status TINYINT DEFAULT 1,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP
);
```

### Game Table Structure
```sql
CREATE TABLE games (
    id INT PRIMARY KEY AUTO_INCREMENT,
    code INT UNIQUE NOT NULL,
    name VARCHAR(255) NOT NULL,
    cover VARCHAR(500),
    channel_ids TEXT,
    status TINYINT DEFAULT 1,
    latest_version VARCHAR(50),
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP
);
```

## Environment Configuration

### 1. API Base URL

Configure the SDK API base URL in your environment:

```bash
# .env
SDK_API_BASE_URL=https://open.anyigame.cn
```

### 2. Database Connection

Ensure proper database configuration for the user plugin:

```php
// config/database.php
'plugin.admin.mysql' => [
    'driver' => 'mysql',
    'host' => env('DB_HOST', '127.0.0.1'),
    'port' => env('DB_PORT', '3306'),
    'database' => env('DB_DATABASE', 'your_database'),
    'username' => env('DB_USERNAME', 'root'),
    'password' => env('DB_PASSWORD', ''),
    'charset' => 'utf8mb4',
    'collation' => 'utf8mb4_unicode_ci',
],
```

## Testing Configuration

### 1. Debug Mode

Enable debug mode for testing:

```php
// config/app.php
'debug' => env('APP_DEBUG', true),
```

### 2. Test Channel

Create a test channel for integration testing:

```sql
INSERT INTO channels (code, name, status) 
VALUES ('test_partner', 'Test Partner Channel', 1);
```

### 3. Test Game

Create a test game for validation:

```sql
INSERT INTO games (code, name, channel_ids, status, latest_version)
VALUES (9999, 'Test Game', '999', 1, '1.0.0');
```

## Monitoring and Logging

### 1. Log Configuration

Monitor integration activity in logs:

```php
// Check logs for integration issues
tail -f storage/logs/webman.log | grep "Channel\|Game\|SDK"
```

### 2. Performance Monitoring

Monitor API performance:

```php
// Heartbeat API monitoring is built-in
// Check HeartbeatMonitoringMiddleware logs
```

---

**Next**: Continue to [Channel Link Generation](./channel-link-generation.md) for URL generation details.
