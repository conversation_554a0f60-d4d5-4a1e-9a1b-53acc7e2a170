# Troubleshooting Guide

## Overview

This guide provides solutions to common issues encountered during third-party SDK integration, debugging techniques, and support procedures.

## Common Integration Issues

### 1. SDK Not Loading

**Symptoms**:
- `AdSDK is not defined` error
- SDK initialization fails
- No SDK-related console logs

**Possible Causes & Solutions**:

#### Cause: Incorrect SDK URL
```javascript
// ❌ Wrong
<script src="https://open.anyigame.cn/sdk/wrong-name.js"></script>

// ✅ Correct
<script src="https://open.anyigame.cn/sdk/ad-sdk.umd.js"></script>
```

#### Cause: HTTPS/HTTP Mixed Content
```javascript
// ❌ Wrong - HTTP on HTTPS page
<script src="http://open.anyigame.cn/sdk/ad-sdk.umd.js"></script>

// ✅ Correct - Always use HTTPS
<script src="https://open.anyigame.cn/sdk/ad-sdk.umd.js"></script>
```

#### Cause: Script Loading Order
```html
<!-- ❌ Wrong - Using SDK before it loads -->
<script>
    AdSDK.GameSDK.getInstance(); // Error: AdSDK not defined
</script>
<script src="https://open.anyigame.cn/sdk/ad-sdk.umd.js"></script>

<!-- ✅ Correct - Load SDK first -->
<script src="https://open.anyigame.cn/sdk/ad-sdk.umd.js"></script>
<script>
    window.addEventListener('load', () => {
        AdSDK.GameSDK.getInstance();
    });
</script>
```

### 2. User Authentication Failures

**Symptoms**:
- "User not logged in" errors
- Empty user information
- Authentication redirects

**Possible Causes & Solutions**:

#### Cause: Missing User Parameters
```javascript
// ❌ Wrong - No user parameter
https://open.anyigame.cn/channel/partner123/game/1001

// ✅ Correct - Include user parameter
https://open.anyigame.cn/channel/partner123/game/1001?user_id=player123
```

#### Cause: Invalid Parameter Format
```javascript
// ❌ Wrong - Invalid characters
?user_id=user<script>alert('xss')</script>

// ✅ Correct - Clean parameter
?user_id=player123
```

#### Cause: Parameter Strategy Mismatch
```php
// Check channel strategy configuration
// If partner uses 'player_id' but strategy expects 'user_id'

// ❌ Wrong strategy
CustomChannelParameterStrategy::forParameter('user_id')

// ✅ Correct strategy  
CustomChannelParameterStrategy::forParameter('player_id')
```

### 3. Cross-Origin (CORS) Errors

**Symptoms**:
- "Access to fetch blocked by CORS policy"
- Network requests failing
- Console CORS errors

**Solutions**:

#### For Iframe Integration
```javascript
// ✅ Use parent window SDK instead of direct API calls
if (window.parent && window.parent.AdSDK) {
    // Use parent SDK
    await window.parent.AdSDK.showAd();
} else {
    console.error('Parent SDK not available');
}
```

#### For Direct Integration
```javascript
// Contact support to add your domain to CORS whitelist
// Domains must be added to backend CORS configuration
```

### 4. Heartbeat System Issues

**Symptoms**:
- Session timeouts
- "Session not found" errors
- Heartbeat API failures

**Solutions**:

#### Cause: Session Expired
```javascript
// ✅ Implement session refresh
try {
    await sendHeartbeat();
} catch (error) {
    if (error.message.includes('session')) {
        // Refresh page to re-authenticate
        window.location.reload();
    }
}
```

#### Cause: Heartbeat Too Frequent
```javascript
// ❌ Wrong - Too frequent (rate limited)
setInterval(sendHeartbeat, 5000); // Every 5 seconds

// ✅ Correct - Appropriate interval
setInterval(sendHeartbeat, 30000); // Every 30 seconds
```

### 5. Player Data Backup Issues

**Symptoms**:
- Data not saving
- Data corruption
- Backup/retrieval failures

**Solutions**:

#### Cause: Data Too Large
```javascript
// ❌ Wrong - Data too large (>1MB)
const hugeData = { /* massive object */ };
await AdSDK.backupPlayerData(hugeData);

// ✅ Correct - Compress or reduce data
const compressedData = compressGameData(gameData);
await AdSDK.backupPlayerData(compressedData);
```

#### Cause: Invalid Data Format
```javascript
// ❌ Wrong - Circular references
const invalidData = {};
invalidData.self = invalidData;

// ✅ Correct - Clean JSON data
const validData = {
    level: 5,
    score: 1000,
    items: ['sword', 'shield']
};
```

## Debugging Techniques

### 1. Enable Debug Mode

```javascript
// Enable debug logging
await sdk.init({
    appid: 'your_game_code',
    channel: 'your_channel_code',
    debug: true // Enable debug mode
});
```

### 2. Console Debugging

```javascript
// Add comprehensive logging
console.log('SDK State:', sdk.getState());
console.log('User Info:', await AdSDK.getUserInfo());
console.log('Session ID:', session().getId());

// Monitor network requests
window.addEventListener('beforeunload', () => {
    console.log('Page unloading, session ending');
});
```

### 3. Network Monitoring

```javascript
// Monitor API calls
const originalFetch = window.fetch;
window.fetch = function(...args) {
    console.log('API Call:', args[0]);
    return originalFetch.apply(this, args)
        .then(response => {
            console.log('API Response:', response.status);
            return response;
        })
        .catch(error => {
            console.error('API Error:', error);
            throw error;
        });
};
```

### 4. Error Tracking

```javascript
// Comprehensive error handling
window.addEventListener('error', (event) => {
    console.error('Global Error:', event.error);
    // Send error to your logging system
});

window.addEventListener('unhandledrejection', (event) => {
    console.error('Unhandled Promise Rejection:', event.reason);
    // Send error to your logging system
});
```

## Browser-Specific Issues

### Chrome Issues

#### Issue: Third-party cookies blocked
```javascript
// Solution: Use SameSite=None for cookies
// Contact support for cookie configuration
```

#### Issue: Mixed content warnings
```javascript
// Solution: Ensure all resources use HTTPS
// Check for HTTP resources in HTTPS context
```

### Safari Issues

#### Issue: Iframe restrictions
```javascript
// Solution: Check iframe sandbox attributes
// Ensure proper iframe permissions
```

#### Issue: Local storage limitations
```javascript
// Solution: Handle storage quota exceeded
try {
    localStorage.setItem('key', 'value');
} catch (error) {
    if (error.name === 'QuotaExceededError') {
        // Handle storage full
        console.warn('Local storage full');
    }
}
```

### Mobile Browser Issues

#### Issue: Viewport scaling
```html
<!-- Add proper viewport meta tag -->
<meta name="viewport" content="width=device-width, initial-scale=1.0, user-scalable=no">
```

#### Issue: Touch event conflicts
```javascript
// Prevent default touch behaviors that might interfere
document.addEventListener('touchstart', (e) => {
    // Handle touch events appropriately
}, { passive: false });
```

## Performance Issues

### 1. Slow SDK Initialization

**Diagnosis**:
```javascript
const startTime = performance.now();
await sdk.init(config);
const duration = performance.now() - startTime;
console.log(`SDK init took ${duration}ms`);
```

**Solutions**:
- Preload SDK script
- Initialize SDK early in page lifecycle
- Check network connectivity

### 2. Memory Leaks

**Diagnosis**:
```javascript
// Monitor memory usage
setInterval(() => {
    if (performance.memory) {
        console.log('Memory usage:', performance.memory.usedJSHeapSize / 1024 / 1024, 'MB');
    }
}, 10000);
```

**Solutions**:
- Remove event listeners properly
- Clear intervals and timeouts
- Avoid circular references

## Support Procedures

### 1. Information to Collect

When reporting issues, provide:

**Basic Information**:
- Channel code
- Game code
- Browser and version
- Operating system
- Error messages (exact text)
- Steps to reproduce

**Technical Information**:
```javascript
// Collect debug information
const debugInfo = {
    userAgent: navigator.userAgent,
    url: window.location.href,
    timestamp: new Date().toISOString(),
    sdkVersion: AdSDK.version,
    sdkState: sdk.getState(),
    sessionId: session()?.getId(),
    errors: [] // Collect any error messages
};
console.log('Debug Info:', JSON.stringify(debugInfo, null, 2));
```

### 2. Log Collection

```javascript
// Collect relevant logs
const logs = [];
const originalConsole = console.log;
console.log = function(...args) {
    logs.push({ timestamp: Date.now(), level: 'log', message: args });
    originalConsole.apply(console, args);
};

// Export logs for support
function exportLogs() {
    const logData = JSON.stringify(logs, null, 2);
    const blob = new Blob([logData], { type: 'application/json' });
    const url = URL.createObjectURL(blob);
    const a = document.createElement('a');
    a.href = url;
    a.download = 'sdk-logs.json';
    a.click();
}
```

### 3. Contact Information

**Technical Support**:
- Email: <EMAIL>
- Response time: 24-48 hours
- Include debug information and logs

**Emergency Support**:
- For production issues affecting live games
- Include impact assessment and urgency level

### 4. Escalation Process

1. **Level 1**: Technical documentation and troubleshooting
2. **Level 2**: Direct technical support contact
3. **Level 3**: Engineering team involvement
4. **Level 4**: Management escalation for critical issues

## Quick Reference

### Common Error Codes

| Code | Message | Solution |
|------|---------|----------|
| 400 | Invalid request parameters | Check parameter format and required fields |
| 401 | User not logged in | Verify user authentication and session |
| 403 | Forbidden | Check channel permissions and CORS |
| 404 | Channel/Game not found | Verify channel and game codes |
| 429 | Rate limit exceeded | Reduce request frequency |
| 500 | Internal server error | Contact support |

### Emergency Fixes

```javascript
// Quick session refresh
if (error.code === 401) {
    window.location.reload();
}

// Fallback for SDK unavailable
if (typeof AdSDK === 'undefined') {
    // Implement fallback behavior
    console.warn('SDK not available, using fallback');
}

// Network error retry
async function retryRequest(fn, maxRetries = 3) {
    for (let i = 0; i < maxRetries; i++) {
        try {
            return await fn();
        } catch (error) {
            if (i === maxRetries - 1) throw error;
            await new Promise(resolve => setTimeout(resolve, 1000 * (i + 1)));
        }
    }
}
```

---

**End of Documentation**

For additional support, please contact our technical team with the information and logs collected using the procedures outlined above.
