# Channel Link Generation Guide

## Overview

This guide explains how to generate appropriate channel links for third-party partners. Channel links are the URLs that partners use to direct their users to games on our platform with proper user identification and parameter passing.

## Link Format Structure

### Basic Format
```
https://open.anyigame.cn/channel/{CHANNEL_CODE}/game/{GAME_CODE}?{USER_PARAMETERS}
```

### Components

1. **Base URL**: `https://open.anyigame.cn`
2. **Channel Code**: Unique identifier for the partner
3. **Game Code**: Numeric identifier for the specific game
4. **User Parameters**: Query parameters for user identification

## Channel Link Examples

### Example 1: Simple User ID
```
https://open.anyigame.cn/channel/partner123/game/1001?user_id=player456
```

### Example 2: Multiple Parameters
```
https://open.anyigame.cn/channel/partner123/game/1001?user_id=player456&level=5&vip=true
```

### Example 3: Custom Parameter Names
```
https://open.anyigame.cn/channel/wechat_partner/game/1001?openid=wx_abc123&nickname=PlayerName
```

### Example 4: Complex User Data
```
https://open.anyigame.cn/channel/partner123/game/1001?uid=12345&username=john_doe&email=<EMAIL>&referrer=campaign_001
```

## Parameter Strategies by Partner Type

### Standard Partners
Most partners use common parameter names:

```
# Standard user ID parameter
?user_id=12345

# Alternative standard parameters
?id=12345
?player_id=12345
?uid=12345
```

### Social Platform Partners

#### WeChat Mini Games
```
?openid=wx_abc123456&unionid=union_xyz789&nickname=PlayerName
```

#### Facebook Games
```
?fb_user_id=**********&fb_name=John+Doe
```

#### Google Play Games
```
?google_id=google_abc123&google_name=Player
```

### Custom Integration Partners

#### Partner with Custom User System
```
?account=custom_user_123&session_token=abc123xyz
```

#### Partner with Email-based System
```
?email=<EMAIL>&auth_token=token123
```

## Link Generation Methods

### Method 1: Manual Generation

For simple integrations, partners can manually construct links:

```javascript
// Partner's JavaScript code
function generateGameLink(userId, gameCode) {
    const baseUrl = 'https://open.anyigame.cn';
    const channelCode = 'YOUR_CHANNEL_CODE';
    const userParam = 'user_id=' + encodeURIComponent(userId);
    
    return `${baseUrl}/channel/${channelCode}/game/${gameCode}?${userParam}`;
}

// Usage
const gameUrl = generateGameLink('player123', '1001');
window.open(gameUrl, '_blank');
```

### Method 2: API-based Generation

For dynamic link generation, use our API:

```javascript
// Request link generation from our API
async function generateChannelLink(gameCode, userParams) {
    const response = await fetch('https://open.anyigame.cn/api/generate-link', {
        method: 'POST',
        headers: {
            'Content-Type': 'application/json',
            'X-Channel-Code': 'YOUR_CHANNEL_CODE'
        },
        body: JSON.stringify({
            game_code: gameCode,
            user_params: userParams
        })
    });
    
    const data = await response.json();
    return data.link;
}

// Usage
const link = await generateChannelLink('1001', {
    user_id: 'player123',
    level: 5,
    vip: true
});
```

### Method 3: Server-side Generation

For server-side applications:

```php
// PHP example
class ChannelLinkGenerator 
{
    private $baseUrl = 'https://open.anyigame.cn';
    private $channelCode;
    
    public function __construct($channelCode) 
    {
        $this->channelCode = $channelCode;
    }
    
    public function generateLink($gameCode, $userParams = []) 
    {
        $url = "{$this->baseUrl}/channel/{$this->channelCode}/game/{$gameCode}";
        
        if (!empty($userParams)) {
            $queryString = http_build_query($userParams);
            $url .= "?" . $queryString;
        }
        
        return $url;
    }
}

// Usage
$generator = new ChannelLinkGenerator('partner123');
$link = $generator->generateLink('1001', [
    'user_id' => 'player456',
    'level' => 10
]);
```

## Parameter Validation

### Required Parameters

Each channel must provide at least one user identification parameter:

```
# At minimum, one of these must be present:
- id
- user_id  
- player_id
- uid
- username
- openid (for WeChat)
- custom parameter (as configured)
```

### Parameter Format Rules

1. **Length**: 1-50 characters
2. **Pattern**: Alphanumeric, underscore, hyphen, @ and . allowed
3. **Encoding**: URL-encoded for special characters

### Validation Examples

```javascript
// Valid user IDs
user_id=player123        ✓
user_id=<EMAIL>  ✓
user_id=player_123-456   ✓

// Invalid user IDs  
user_id=                 ✗ (empty)
user_id=user with spaces ✗ (spaces, should be encoded)
user_id=user<script>     ✗ (invalid characters)
```

## SDK Configuration Parameters

### Optional SDK Parameters

Add these parameters to control SDK behavior:

```
# Enable/disable SDK features
?need_sdk=1              # Enable SDK (default)
?need_sdk=0              # Disable SDK

# Debug mode
?debug=1                 # Enable debug logging
?debug=0                 # Disable debug logging (default)

# Custom configuration
?sdk_config=custom       # Use custom SDK configuration
```

### Complete Example with SDK Parameters
```
https://open.anyigame.cn/channel/partner123/game/1001?user_id=player456&need_sdk=1&debug=0
```

## Security Considerations

### 1. Parameter Sanitization

Always sanitize user parameters:

```javascript
function sanitizeUserId(userId) {
    // Remove potentially dangerous characters
    return userId.replace(/[<>\"'&]/g, '');
}
```

### 2. HTTPS Only

Always use HTTPS for channel links:
```
✓ https://open.anyigame.cn/channel/...
✗ http://open.anyigame.cn/channel/...
```

### 3. Parameter Encoding

Properly encode special characters:

```javascript
const userId = '<EMAIL>';
const encodedUserId = encodeURIComponent(userId);
// Result: user%40domain.com
```

## Testing Channel Links

### 1. Manual Testing

Test links manually in browser:
```
https://open.anyigame.cn/channel/test_partner/game/9999?user_id=test_user
```

### 2. Automated Testing

```javascript
// Test link generation
function testChannelLink() {
    const link = generateGameLink('test_user', '9999');
    console.log('Generated link:', link);
    
    // Validate link format
    const urlPattern = /^https:\/\/open\.anyigame\.cn\/channel\/[a-zA-Z0-9_]+\/game\/\d+/;
    if (urlPattern.test(link)) {
        console.log('✓ Link format is valid');
    } else {
        console.log('✗ Link format is invalid');
    }
}
```

### 3. Integration Testing

Use our test environment:
```
https://test.anyigame.cn/channel/YOUR_CHANNEL/game/9999?user_id=test_user
```

## Common Issues and Solutions

### Issue 1: User Not Recognized
**Problem**: User parameter not being extracted
**Solution**: Check parameter strategy configuration

### Issue 2: Game Not Loading
**Problem**: Game not associated with channel
**Solution**: Verify game's `channel_ids` includes your channel ID

### Issue 3: Authentication Failure
**Problem**: User cannot be authenticated
**Solution**: Ensure user parameter format matches validation rules

### Issue 4: CORS Errors
**Problem**: Cross-origin request blocked
**Solution**: Add your domain to CORS whitelist

---

**Next**: Continue to [Technical Specifications](./technical-specifications.md) for detailed API documentation.
