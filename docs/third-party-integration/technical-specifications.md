# Technical Specifications

## Overview

This document provides detailed technical specifications for third-party SDK integration, including API endpoints, authentication methods, data formats, and technical constraints.

## API Endpoints

### Base URL
```
Production: https://open.anyigame.cn
Testing: https://test.anyigame.cn
```

### 1. Game Configuration API

**Endpoint**: `GET /api/game/config`

**Purpose**: Retrieve game configuration and validate channel access

**Headers**:
```
X-Channel-Code: {CHANNEL_CODE}
Content-Type: application/json
```

**Parameters**:
```
code: {GAME_CODE} (required, integer)
```

**Request Example**:
```bash
curl -X GET "https://open.anyigame.cn/api/game/config?code=1001" \
  -H "X-Channel-Code: partner123" \
  -H "Content-Type: application/json"
```

**Response Format**:
```json
{
  "code": 0,
  "msg": "Success",
  "data": {
    "id": 1,
    "code": 1001,
    "name": "Puzzle Game",
    "cover": "/images/puzzle-cover.jpg",
    "channel_ids": "123,124,125",
    "status": 1,
    "latest_version": "1.0.0",
    "created_at": "2024-01-01 00:00:00",
    "updated_at": "2024-01-01 00:00:00"
  }
}
```

**Error Responses**:
```json
// Invalid channel
{
  "code": 404,
  "msg": "Channel not enabled"
}

// Game not found or not associated with channel
{
  "code": 404,
  "msg": "Game not found or not associated with channel"
}
```

### 2. Heartbeat API

**Endpoint**: `POST /api/heartbeat/heartbeat`

**Purpose**: Maintain active session and track user activity

**Authentication**: Required (user must be logged in)

**Rate Limit**: 10 requests per minute

**Headers**:
```
Content-Type: application/json
Cookie: {SESSION_COOKIE}
```

**Request Body**:
```json
{
  "game_data": {
    "level": 5,
    "score": 1200,
    "play_time": 300
  },
  "client_info": {
    "screen_width": 1920,
    "screen_height": 1080,
    "user_agent": "Mozilla/5.0..."
  }
}
```

**Response Format**:
```json
{
  "code": 200,
  "msg": "success",
  "data": {
    "session_id": "sess_abc123",
    "user_id": 456,
    "last_heartbeat": "2024-01-01 12:00:00",
    "session_duration": 1800
  }
}
```

### 3. Player Data Backup API

**Endpoint**: `POST /api/player-data/backup`

**Purpose**: Backup player game progress and data

**Authentication**: Required

**Headers**:
```
Content-Type: application/json
X-Channel-Code: {CHANNEL_CODE}
```

**Request Body**:
```json
{
  "game_code": 1001,
  "player_data": {
    "level": 10,
    "score": 5000,
    "items": ["sword", "shield", "potion"],
    "achievements": ["first_win", "level_10"],
    "settings": {
      "sound": true,
      "music": false
    }
  },
  "metadata": {
    "version": "1.0.0",
    "timestamp": 1704067200,
    "checksum": "abc123def456"
  }
}
```

**Response Format**:
```json
{
  "code": 200,
  "msg": "success",
  "data": {
    "backup_id": "backup_xyz789",
    "backup_key": "key_abc123",
    "created_at": "2024-01-01 12:00:00"
  }
}
```

### 4. Player Data Retrieval API

**Endpoint**: `GET /api/player-data/retrieve`

**Purpose**: Retrieve saved player data

**Authentication**: Required

**Headers**:
```
X-Channel-Code: {CHANNEL_CODE}
```

**Parameters**:
```
game_code: {GAME_CODE} (required, integer)
backup_key: {BACKUP_KEY} (optional, string)
```

**Response Format**:
```json
{
  "code": 200,
  "msg": "success",
  "data": {
    "backup_id": "backup_xyz789",
    "game_code": 1001,
    "player_data": {
      "level": 10,
      "score": 5000,
      "items": ["sword", "shield", "potion"],
      "achievements": ["first_win", "level_10"],
      "settings": {
        "sound": true,
        "music": false
      }
    },
    "metadata": {
      "version": "1.0.0",
      "timestamp": 1704067200,
      "checksum": "abc123def456"
    },
    "created_at": "2024-01-01 12:00:00",
    "updated_at": "2024-01-01 12:00:00"
  }
}
```

### 5. User Information API

**Endpoint**: `GET /api/user/me`

**Purpose**: Get current authenticated user information

**Authentication**: Required

**Response Format**:
```json
{
  "code": 200,
  "msg": "success",
  "data": {
    "id": 456,
    "username": "player123",
    "nickname": "Player Name",
    "channel_id": 123,
    "avatar": "/images/avatar.jpg",
    "email": "<EMAIL>",
    "level": 5,
    "score": 1000,
    "last_time": "2024-01-01 12:00:00",
    "join_time": "2024-01-01 00:00:00"
  }
}
```

## Authentication Methods

### 1. Session-based Authentication

**Method**: PHP Sessions with automatic user registration

**Flow**:
1. User accesses channel link with user parameters
2. System extracts user identifier using channel strategy
3. System finds existing user or creates new user automatically
4. Session is established with user data
5. SDK APIs use session for authentication

**Session Data Structure**:
```php
$_SESSION['user'] = [
    'id' => 456,
    'username' => 'player123',
    'nickname' => 'Player Name',
    'channel_id' => 123,
    'avatar' => '/images/avatar.jpg',
    'email' => '<EMAIL>',
    'mobile' => '+1234567890'
];
```

### 2. Channel Code Validation

**Method**: X-Channel-Code header validation

**Usage**: All API requests must include valid channel code

**Validation Process**:
1. Extract channel code from X-Channel-Code header
2. Verify channel exists and is active
3. Validate channel has access to requested resources

## Data Formats

### 1. Standard Response Format

All API responses follow this structure:

```json
{
  "code": 200,           // HTTP-like status code
  "msg": "success",      // Human-readable message
  "data": {}            // Response data (varies by endpoint)
}
```

**Status Codes**:
- `0` or `200`: Success
- `400`: Bad Request
- `401`: Unauthorized
- `403`: Forbidden
- `404`: Not Found
- `429`: Rate Limit Exceeded
- `500`: Internal Server Error

### 2. Error Response Format

```json
{
  "code": 400,
  "msg": "Invalid request parameters",
  "data": null
}
```

### 3. Player Data Format

Player data should be valid JSON with these constraints:

**Size Limits**:
- Maximum size: 1MB per backup
- Maximum nesting depth: 10 levels
- Maximum array length: 1000 items

**Supported Data Types**:
- Strings, Numbers, Booleans
- Arrays and Objects
- null values

**Example Structure**:
```json
{
  "progress": {
    "level": 10,
    "experience": 2500,
    "completed_levels": [1, 2, 3, 4, 5]
  },
  "inventory": {
    "items": [
      {"id": "sword", "quantity": 1, "level": 5},
      {"id": "potion", "quantity": 10, "level": 1}
    ],
    "currency": {
      "gold": 1000,
      "gems": 50
    }
  },
  "settings": {
    "sound_enabled": true,
    "music_volume": 0.8,
    "language": "en"
  }
}
```

## Technical Constraints

### 1. Rate Limiting

**Heartbeat API**: 10 requests per minute per user
**Backup API**: 5 requests per minute per user
**Retrieval API**: 20 requests per minute per user

### 2. Data Size Limits

**Player Data Backup**: 1MB maximum
**Request Body**: 10MB maximum
**URL Length**: 2048 characters maximum

### 3. Session Timeout

**Active Session**: 30 minutes of inactivity
**Heartbeat Interval**: Recommended 30 seconds
**Session Extension**: Each heartbeat extends session

### 4. Browser Compatibility

**Minimum Requirements**:
- Chrome 60+
- Firefox 55+
- Safari 12+
- Edge 79+
- Mobile browsers with ES5 support

### 5. Network Requirements

**Protocol**: HTTPS only
**Ports**: 443 (HTTPS)
**CDN**: CloudFlare or similar recommended
**Latency**: <200ms recommended for optimal experience

### 6. Security Requirements

**CORS**: Configured per partner domain
**CSP**: Content Security Policy headers required
**SSL**: TLS 1.2 minimum
**Headers**: Security headers enforced

## SDK Configuration

### 1. Required Configuration

```javascript
{
  appid: 'string',      // Game code (required)
  channel: 'string',    // Channel code (required)
  debug: boolean        // Debug mode (optional, default: false)
}
```

### 2. Optional Configuration

```javascript
{
  apiBaseUrl: 'string',     // Custom API base URL
  timeout: number,          // Request timeout in ms (default: 30000)
  retryAttempts: number,    // Retry attempts (default: 3)
  heartbeatInterval: number // Heartbeat interval in ms (default: 30000)
}
```

### 3. Environment Detection

The SDK automatically detects:
- Iframe vs direct integration
- Mobile vs desktop
- Development vs production environment
- Available features and capabilities

## Integration Checklist

### Pre-Integration Requirements
- [ ] Channel code assigned
- [ ] Game code assigned
- [ ] Parameter strategy configured
- [ ] Domain whitelist updated
- [ ] Test environment access provided

### Technical Implementation
- [ ] SDK included in game
- [ ] Initialization code implemented
- [ ] Error handling implemented
- [ ] User parameter passing configured
- [ ] HTTPS enabled

### Testing Validation
- [ ] SDK initialization successful
- [ ] User authentication working
- [ ] Heartbeat system functional
- [ ] Player data backup/retrieval working
- [ ] Cross-browser compatibility verified

---

**Next**: Continue to [Testing Guide](./testing-guide.md) for integration validation procedures.
