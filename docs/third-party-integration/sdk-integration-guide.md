# SDK Integration Guide

## Overview

This guide provides step-by-step instructions for integrating our SDK into third-party games. The SDK handles user authentication, session management, advertising, and player data backup.

## SDK Features

- **User Authentication**: Automatic user registration and login
- **Session Management**: Heartbeat-based session tracking
- **Advertising**: Rewarded video ad integration
- **Player Data**: Game progress backup and retrieval
- **Cross-iframe Support**: Seamless iframe communication
- **Event Tracking**: Game analytics and user behavior tracking

## Integration Methods

### Method 1: Iframe Integration (Recommended)

This is the recommended approach where your game runs in an iframe on our platform.

#### Step 1: Prepare Your Game

Ensure your game is hosted on a stable CDN and supports iframe embedding:

```html
<!-- Your game's index.html -->
<!DOCTYPE html>
<html>
<head>
    <meta charset="UTF-8">
    <title>Your Game</title>
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
</head>
<body>
    <!-- Your game content -->
    <div id="gameContainer">
        <!-- Game canvas/content here -->
    </div>

    <!-- SDK Integration -->
    <script>
        // Check if SDK is available in parent window
        if (window.parent && window.parent.AdSDK) {
            // SDK is available, initialize game
            initializeGame();
        } else {
            console.error('SDK not available');
        }

        function initializeGame() {
            // Your game initialization code
            console.log('Game initialized with SDK support');
            
            // Example: Show rewarded ad
            showRewardedAd();
        }

        async function showRewardedAd() {
            try {
                // Request ad from parent window SDK
                const result = await window.parent.AdSDK.showAd();
                if (result.completed) {
                    // User watched the full ad, give reward
                    givePlayerReward();
                }
            } catch (error) {
                console.error('Ad failed:', error);
            }
        }

        function givePlayerReward() {
            // Your reward logic here
            console.log('Player earned reward!');
        }
    </script>
</body>
</html>
```

#### Step 2: Channel Link Format

Your game will be accessed via our channel link format:

```
https://open.anyigame.cn/channel/{CHANNEL_CODE}/game/{GAME_CODE}?{USER_PARAMS}
```

Example:
```
https://open.anyigame.cn/channel/partner123/game/puzzle001?user_id=player456&level=5
```

### Method 2: Direct Integration

For direct integration, include our SDK directly in your game:

#### Step 1: Include SDK

```html
<!-- Include SDK in your game -->
<script src="https://open.anyigame.cn/sdk/ad-sdk.umd.js"></script>
```

#### Step 2: Initialize SDK

```javascript
// Initialize SDK with your configuration
async function initSDK() {
    try {
        // Get SDK instance
        const sdk = AdSDK.GameSDK.getInstance();
        
        // Initialize with your channel configuration
        await sdk.init({
            appid: 'YOUR_GAME_CODE',
            channel: 'YOUR_CHANNEL_CODE',
            debug: false // Set to true for development
        });
        
        console.log('SDK initialized successfully');
        
        // Start your game
        startGame();
        
    } catch (error) {
        console.error('SDK initialization failed:', error);
    }
}

// Call initialization when page loads
window.addEventListener('load', initSDK);
```

## SDK API Reference

### Core Methods

#### Initialize SDK
```javascript
await AdSDK.GameSDK.getInstance().init({
    appid: 'your_game_code',
    channel: 'your_channel_code',
    debug: false
});
```

#### Show Rewarded Ad
```javascript
const result = await AdSDK.showAd((closeType) => {
    if (closeType === AdSDK.AdCloseType.COMPLETED) {
        // User watched full ad
        console.log('Ad completed - give reward');
    } else {
        // User closed ad early
        console.log('Ad closed early - no reward');
    }
});
```

#### Get User Information
```javascript
const userInfo = await AdSDK.getUserInfo();
console.log('User ID:', userInfo.userId);
console.log('Username:', userInfo.username);
```

#### Backup Player Data
```javascript
// Save player progress
await AdSDK.backupPlayerData({
    level: 10,
    score: 1500,
    items: ['sword', 'shield'],
    timestamp: Date.now()
});
```

#### Retrieve Player Data
```javascript
// Load player progress
const playerData = await AdSDK.retrievePlayerData();
if (playerData) {
    console.log('Player level:', playerData.level);
    console.log('Player score:', playerData.score);
}
```

### Event Tracking

```javascript
// Track custom game events
AdSDK.trackEvent('level_completed', {
    level: 5,
    score: 1200,
    time_spent: 180
});

AdSDK.trackEvent('item_purchased', {
    item_id: 'power_up_1',
    cost: 100,
    currency: 'coins'
});
```

## Error Handling

Always implement proper error handling:

```javascript
try {
    await AdSDK.showAd();
} catch (error) {
    console.error('SDK Error:', error.message);
    
    // Handle specific error types
    if (error.code === 'SDK_NOT_INITIALIZED') {
        // Reinitialize SDK
        await initSDK();
    } else if (error.code === 'AD_NOT_AVAILABLE') {
        // Show alternative content
        showAlternativeReward();
    }
}
```

## Best Practices

1. **Always check SDK availability** before making calls
2. **Implement fallbacks** for when SDK features are unavailable
3. **Handle errors gracefully** to maintain game experience
4. **Test thoroughly** in both development and production environments
5. **Use debug mode** during development for detailed logging

---

**Next**: Continue to [Backend Configuration](./backend-configuration.md) for server-side setup.
