# Integration Testing Guide

## Overview

This guide provides comprehensive testing procedures and validation steps for third-party partners to verify their SDK integration works correctly before going live.

## Testing Environment

### Test Environment Access

**Test Base URL**: `https://test.anyigame.cn`

**Test Credentials**:
- Test Channel Code: `test_partner`
- Test Game Code: `9999`
- Test User Parameters: `user_id=test_user`

**Test Link Example**:
```
https://test.anyigame.cn/channel/test_partner/game/9999?user_id=test_user_123
```

## Pre-Integration Testing

### 1. Environment Setup Verification

**Check List**:
- [ ] Game hosted on HTTPS domain
- [ ] Game accessible via direct URL
- [ ] Game supports iframe embedding
- [ ] Game works in landscape orientation
- [ ] All game assets load correctly

**Test Commands**:
```bash
# Test HTTPS certificate
curl -I https://your-game-domain.com/game/index.html

# Test iframe embedding
curl -H "X-Frame-Options: ALLOWALL" https://your-game-domain.com/game/index.html
```

### 2. Basic Connectivity Test

**Test Channel Link Access**:
```
https://test.anyigame.cn/channel/YOUR_TEST_CHANNEL/game/9999?user_id=test_user
```

**Expected Results**:
- [ ] Page loads without errors
- [ ] Game iframe appears
- [ ] No console errors related to loading
- [ ] User authentication completes

## SDK Integration Testing

### 1. SDK Initialization Test

**Test Code**:
```javascript
// Add to your game for testing
async function testSDKInitialization() {
    console.log('Testing SDK initialization...');
    
    try {
        // Check if SDK is available
        if (typeof AdSDK === 'undefined') {
            console.error('❌ SDK not loaded');
            return false;
        }
        
        console.log('✅ SDK loaded successfully');
        
        // Get SDK instance
        const sdk = AdSDK.GameSDK.getInstance();
        console.log('✅ SDK instance obtained');
        
        // Initialize SDK
        await sdk.init({
            appid: 'YOUR_GAME_CODE',
            channel: 'YOUR_CHANNEL_CODE',
            debug: true
        });
        
        console.log('✅ SDK initialized successfully');
        return true;
        
    } catch (error) {
        console.error('❌ SDK initialization failed:', error);
        return false;
    }
}

// Run test
testSDKInitialization().then(success => {
    if (success) {
        console.log('🎉 SDK integration test passed!');
    } else {
        console.log('💥 SDK integration test failed!');
    }
});
```

### 2. User Authentication Test

**Test Code**:
```javascript
async function testUserAuthentication() {
    console.log('Testing user authentication...');
    
    try {
        // Get user information
        const userInfo = await AdSDK.getUserInfo();
        
        console.log('✅ User info retrieved:', userInfo);
        
        // Validate user data
        if (userInfo && userInfo.userId) {
            console.log('✅ User authenticated successfully');
            console.log('User ID:', userInfo.userId);
            console.log('Username:', userInfo.username);
            return true;
        } else {
            console.error('❌ User authentication failed - no user data');
            return false;
        }
        
    } catch (error) {
        console.error('❌ User authentication error:', error);
        return false;
    }
}
```

### 3. Heartbeat System Test

**Test Code**:
```javascript
async function testHeartbeatSystem() {
    console.log('Testing heartbeat system...');
    
    try {
        // Send test heartbeat
        const heartbeatData = {
            game_data: {
                level: 1,
                score: 100,
                play_time: 60
            },
            client_info: {
                screen_width: window.screen.width,
                screen_height: window.screen.height,
                user_agent: navigator.userAgent
            }
        };
        
        const response = await fetch('/api/heartbeat/heartbeat', {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json'
            },
            body: JSON.stringify(heartbeatData)
        });
        
        const result = await response.json();
        
        if (result.code === 200) {
            console.log('✅ Heartbeat sent successfully:', result);
            return true;
        } else {
            console.error('❌ Heartbeat failed:', result);
            return false;
        }
        
    } catch (error) {
        console.error('❌ Heartbeat error:', error);
        return false;
    }
}
```

### 4. Player Data Backup Test

**Test Code**:
```javascript
async function testPlayerDataBackup() {
    console.log('Testing player data backup...');
    
    try {
        // Test data to backup
        const testData = {
            level: 5,
            score: 1500,
            items: ['sword', 'shield'],
            achievements: ['first_win'],
            settings: {
                sound: true,
                music: false
            }
        };
        
        // Backup data
        const backupResult = await AdSDK.backupPlayerData(testData);
        console.log('✅ Data backup successful:', backupResult);
        
        // Retrieve data
        const retrievedData = await AdSDK.retrievePlayerData();
        console.log('✅ Data retrieval successful:', retrievedData);
        
        // Validate data integrity
        if (JSON.stringify(testData) === JSON.stringify(retrievedData)) {
            console.log('✅ Data integrity verified');
            return true;
        } else {
            console.error('❌ Data integrity check failed');
            console.log('Original:', testData);
            console.log('Retrieved:', retrievedData);
            return false;
        }
        
    } catch (error) {
        console.error('❌ Player data backup/retrieval error:', error);
        return false;
    }
}
```

## Comprehensive Test Suite

### Automated Test Runner

**Complete Test Suite**:
```javascript
class SDKIntegrationTester {
    constructor(config) {
        this.config = config;
        this.results = [];
    }
    
    async runAllTests() {
        console.log('🚀 Starting SDK Integration Test Suite...');
        
        const tests = [
            { name: 'SDK Initialization', test: this.testSDKInit.bind(this) },
            { name: 'User Authentication', test: this.testUserAuth.bind(this) },
            { name: 'Heartbeat System', test: this.testHeartbeat.bind(this) },
            { name: 'Player Data Backup', test: this.testDataBackup.bind(this) },
            { name: 'Ad System', test: this.testAdSystem.bind(this) },
            { name: 'Error Handling', test: this.testErrorHandling.bind(this) }
        ];
        
        for (const testCase of tests) {
            console.log(`\n📋 Running test: ${testCase.name}`);
            try {
                const result = await testCase.test();
                this.results.push({ name: testCase.name, passed: result, error: null });
                console.log(result ? '✅ PASSED' : '❌ FAILED');
            } catch (error) {
                this.results.push({ name: testCase.name, passed: false, error: error.message });
                console.log('❌ ERROR:', error.message);
            }
        }
        
        this.printSummary();
    }
    
    async testSDKInit() {
        const sdk = AdSDK.GameSDK.getInstance();
        await sdk.init(this.config);
        return sdk.getState() === 'INITIALIZED';
    }
    
    async testUserAuth() {
        const userInfo = await AdSDK.getUserInfo();
        return userInfo && userInfo.userId;
    }
    
    async testHeartbeat() {
        // Implementation from previous example
        return true; // Simplified for brevity
    }
    
    async testDataBackup() {
        // Implementation from previous example
        return true; // Simplified for brevity
    }
    
    async testAdSystem() {
        try {
            // Test ad availability (don't actually show)
            const adAvailable = await AdSDK.isAdAvailable();
            return typeof adAvailable === 'boolean';
        } catch (error) {
            return false;
        }
    }
    
    async testErrorHandling() {
        try {
            // Test invalid API call
            await AdSDK.invalidMethod();
            return false; // Should have thrown error
        } catch (error) {
            return true; // Error handling working
        }
    }
    
    printSummary() {
        console.log('\n📊 Test Summary:');
        console.log('================');
        
        const passed = this.results.filter(r => r.passed).length;
        const total = this.results.length;
        
        this.results.forEach(result => {
            const status = result.passed ? '✅' : '❌';
            console.log(`${status} ${result.name}`);
            if (result.error) {
                console.log(`   Error: ${result.error}`);
            }
        });
        
        console.log(`\nResults: ${passed}/${total} tests passed`);
        
        if (passed === total) {
            console.log('🎉 All tests passed! Integration ready for production.');
        } else {
            console.log('💥 Some tests failed. Please fix issues before proceeding.');
        }
    }
}

// Usage
const tester = new SDKIntegrationTester({
    appid: 'YOUR_GAME_CODE',
    channel: 'YOUR_CHANNEL_CODE',
    debug: true
});

tester.runAllTests();
```

## Manual Testing Checklist

### 1. Basic Functionality
- [ ] Game loads in iframe without errors
- [ ] User authentication completes automatically
- [ ] Game receives user information correctly
- [ ] Heartbeat system maintains session
- [ ] Player data backup/retrieval works

### 2. Cross-Browser Testing
- [ ] Chrome (latest)
- [ ] Firefox (latest)
- [ ] Safari (latest)
- [ ] Edge (latest)
- [ ] Mobile Chrome
- [ ] Mobile Safari

### 3. Network Conditions
- [ ] Fast connection (>10 Mbps)
- [ ] Slow connection (<1 Mbps)
- [ ] Intermittent connection
- [ ] Connection loss/recovery

### 4. Error Scenarios
- [ ] Invalid user parameters
- [ ] Network timeouts
- [ ] Server errors
- [ ] SDK initialization failures
- [ ] Session expiration

## Performance Testing

### 1. Load Time Metrics
```javascript
// Measure SDK initialization time
const startTime = performance.now();
await sdk.init(config);
const initTime = performance.now() - startTime;
console.log(`SDK initialization took ${initTime}ms`);
```

### 2. Memory Usage
```javascript
// Monitor memory usage
const memoryBefore = performance.memory?.usedJSHeapSize || 0;
await sdk.init(config);
const memoryAfter = performance.memory?.usedJSHeapSize || 0;
console.log(`SDK memory usage: ${(memoryAfter - memoryBefore) / 1024 / 1024}MB`);
```

## Production Readiness Checklist

### Final Validation
- [ ] All automated tests pass
- [ ] Manual testing completed
- [ ] Performance metrics acceptable
- [ ] Error handling robust
- [ ] Documentation reviewed
- [ ] Security requirements met
- [ ] HTTPS certificate valid
- [ ] CORS configuration correct

### Go-Live Approval
- [ ] Technical team approval
- [ ] Business team approval
- [ ] Production environment configured
- [ ] Monitoring systems in place
- [ ] Support procedures documented

---

**Next**: Continue to [Troubleshooting](./troubleshooting.md) for common issues and solutions.
