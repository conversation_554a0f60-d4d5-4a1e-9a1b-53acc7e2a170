# Third-Party SDK Integration Documentation

## Overview

This documentation provides comprehensive guidance for third-party partners who want to integrate our SDK into their games. Our platform supports landscape-oriented games with flexible channel management and automatic user authentication.

## Table of Contents

1. [Integration Framework](#integration-framework)
2. [SDK Integration Guide](#sdk-integration-guide)
3. [Backend Configuration](#backend-configuration)
4. [Channel Link Generation](#channel-link-generation)
5. [Technical Specifications](#technical-specifications)
6. [Testing Guide](#testing-guide)
7. [Troubleshooting](#troubleshooting)

## Integration Framework

### Architecture Overview

```
Third-Party Game (CDN) → Our SDK → Our Backend APIs
                      ↓
                   Channel System → User Authentication → Game Session
```

### Key Components

1. **Channel Management**: Each partner gets a unique channel configuration
2. **SDK Integration**: JavaScript SDK embedded in partner's game
3. **User Authentication**: Automatic user registration/login based on channel parameters
4. **Session Tracking**: Heartbeat system for active session monitoring
5. **Data Backup**: Player progress and game data synchronization

### Integration Types

#### Type 1: Iframe Integration (Recommended)
- Partner hosts game on their CDN
- Game loads in iframe on our platform
- SDK initialized in parent window
- Cross-iframe communication for API calls

#### Type 2: Direct Integration
- Partner integrates SDK directly into their game
- Game communicates directly with our APIs
- Requires CORS configuration

## Prerequisites

### Technical Requirements

- **Game Orientation**: Landscape/horizontal orientation
- **JavaScript Support**: ES5+ compatibility
- **HTTPS**: SSL certificate required for production
- **Domain**: Stable domain for CDN hosting

### Partner Information Needed

1. **Company Details**
   - Company name and contact information
   - Technical contact details
   - Business contact for agreements

2. **Technical Details**
   - Game CDN domain(s)
   - User identification parameter name(s)
   - Expected user ID format/pattern
   - Game codes/identifiers

3. **Integration Preferences**
   - User parameter naming convention
   - Authentication flow requirements
   - Data backup requirements

## Quick Start

### Step 1: Channel Setup
Contact our technical team to set up your channel configuration with:
- Unique channel code
- User parameter strategy
- Domain whitelist
- Game associations

### Step 2: SDK Integration
Add our SDK to your game and initialize with your channel configuration.

### Step 3: Testing
Use our testing environment to validate the integration.

### Step 4: Production Deployment
Deploy to production after successful testing and approval.

## Support

For technical support and questions:
- **Technical Documentation**: See detailed guides in this documentation
- **Integration Support**: Contact our technical team
- **Testing Environment**: Available for integration validation

---

**Next**: Continue to [SDK Integration Guide](./sdk-integration-guide.md) for detailed implementation instructions.
